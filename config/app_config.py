"""
应用配置模块
使用 Pydantic 管理应用配置，支持环境变量和配置文件
"""

import os
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from pathlib import Path

# 简化版配置，不使用 BaseSettings
class BaseConfig(BaseModel):
    """基础配置类"""
    class Config:
        env_prefix = ""
        case_sensitive = False


class LoggingConfig(BaseConfig):
    """日志配置"""
    level: str = Field(default="INFO", description="日志级别")
    file: Optional[str] = Field(default="agent.log", description="日志文件名")
    dir: str = Field(default="logs", description="日志目录")
    max_file_size: int = Field(default=50 * 1024 * 1024, description="单个日志文件最大大小(字节)")
    backup_count: int = Field(default=10, description="保留的日志文件数量")
    enable_console: bool = Field(default=True, description="是否启用控制台输出")
    enable_colors: bool = Field(default=True, description="是否启用彩色输出")


class LangSmithConfig(BaseConfig):
    """LangSmith 配置"""
    tracing_v2: bool = Field(default=False, description="是否启用 LangSmith 追踪")
    endpoint: str = Field(default="", description="LangSmith 端点")
    api_key: str = Field(default="", description="LangSmith API 密钥")
    project: str = Field(default="", description="LangSmith 项目名称")


class PerformanceConfig(BaseConfig):
    """性能配置"""
    max_retries: int = Field(default=3, description="最大重试次数")
    request_timeout: int = Field(default=60, description="请求超时时间(秒)")
    connection_pool_size: int = Field(default=100, description="连接池大小")
    enable_caching: bool = Field(default=True, description="是否启用缓存")
    cache_ttl: int = Field(default=3600, description="缓存过期时间(秒)")


class SecurityConfig(BaseConfig):
    """安全配置"""
    enable_auth: bool = Field(default=True, description="是否启用身份验证")
    admin_password: str = Field(default="admin123", description="管理员密码")
    session_timeout: int = Field(default=24 * 3600, description="会话超时时间(秒)")
    max_sessions_per_user: int = Field(default=10, description="每用户最大会话数")


class AppConfig(BaseConfig):
    """应用主配置"""
    # 基础配置
    debug_mode: bool = Field(default=False, description="是否启用调试模式")
    environment: str = Field(default="development", description="运行环境")
    host: str = Field(default="localhost", description="服务器主机")
    port: int = Field(default=8000, description="服务器端口")
    
    # 子配置
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    langsmith: LangSmithConfig = Field(default_factory=LangSmithConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    
    # 文件路径配置
    config_dir: str = Field(default="config", description="配置文件目录")
    data_dir: str = Field(default="data", description="数据目录")
    logs_dir: str = Field(default="logs", description="日志目录")
    

    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 设置 LangSmith 环境变量
        self._set_langsmith_env()
        # 创建必要的目录
        self._create_directories()
    
    def _set_langsmith_env(self):
        """设置 LangSmith 环境变量"""
        os.environ["LANGCHAIN_TRACING_V2"] = str(self.langsmith.tracing_v2).lower()
        os.environ["LANGCHAIN_ENDPOINT"] = self.langsmith.endpoint
        os.environ["LANGCHAIN_API_KEY"] = self.langsmith.api_key
        os.environ["LANGCHAIN_PROJECT"] = self.langsmith.project
        os.environ["LANGSMITH_TRACING"] = str(self.langsmith.tracing_v2).lower()
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            self.config_dir,
            self.data_dir,
            self.logs_dir,
            self.logging.dir
        ]
        
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置字典"""
        return {
            "log_level": self.logging.level,
            "log_file": self.logging.file,
            "log_dir": self.logging.dir,
            "max_file_size": self.logging.max_file_size,
            "backup_count": self.logging.backup_count,
            "enable_console": self.logging.enable_console,
            "enable_colors": self.logging.enable_colors and not self.is_production()
        }
    
    def is_production(self) -> bool:
        """判断是否为生产环境"""
        return self.environment.lower() in ["production", "prod"]
    
    def is_development(self) -> bool:
        """判断是否为开发环境"""
        return self.environment.lower() in ["development", "dev"]
    
    def is_testing(self) -> bool:
        """判断是否为测试环境"""
        return self.environment.lower() in ["testing", "test"]
    
    def get_db_path(self, db_name: str) -> str:
        """获取数据库文件路径"""
        return str(Path(self.data_dir) / db_name)
    
    def get_log_path(self, log_name: str) -> str:
        """获取日志文件路径"""
        return str(Path(self.logs_dir) / log_name)
    
    def export_config(self, filepath: str):
        """导出配置到文件"""
        config_dict = self.dict()
        try:
            import json
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            print(f"配置已导出到: {filepath}")
        except Exception as e:
            print(f"导出配置失败: {e}")


# 预定义的环境配置
DEVELOPMENT_CONFIG = {
    "debug_mode": True,
    "environment": "development",
    "logging": {
        "level": "DEBUG",
        "enable_console": True,
        "enable_colors": True,
        "file": None  # 开发环境不写文件
    },
    "performance": {
        "max_retries": 2,
        "enable_caching": False
    }
}

PRODUCTION_CONFIG = {
    "debug_mode": False,
    "environment": "production",
    "logging": {
        "level": "INFO",
        "enable_console": True,
        "enable_colors": False,
        "file": "app.log",
        "max_file_size": 100 * 1024 * 1024,  # 100MB
        "backup_count": 20
    },
    "performance": {
        "max_retries": 5,
        "enable_caching": True,
        "cache_ttl": 7200  # 2小时
    }
}

TESTING_CONFIG = {
    "debug_mode": False,
    "environment": "testing",
    "logging": {
        "level": "WARNING",
        "enable_console": False,
        "file": "test.log"
    },
    "performance": {
        "max_retries": 1,
        "enable_caching": False
    }
}


def load_config(environment: str = None) -> AppConfig:
    """
    加载应用配置
    
    Args:
        environment: 环境名称，如果为None则从环境变量获取
        
    Returns:
        应用配置实例
    """
    if environment is None:
        environment = os.getenv("APP_ENVIRONMENT", "development")
    
    # 根据环境选择预定义配置
    if environment.lower() in ["development", "dev"]:
        base_config = DEVELOPMENT_CONFIG
    elif environment.lower() in ["production", "prod"]:
        base_config = PRODUCTION_CONFIG
    elif environment.lower() in ["testing", "test"]:
        base_config = TESTING_CONFIG
    else:
        base_config = {}
    
    # 合并配置
    config = AppConfig(**base_config)
    return config


# 全局配置实例
app_config = load_config()
