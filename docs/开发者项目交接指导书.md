# LangGraph Agent 项目开发者交接指导书

## 📋 项目概述

### 项目基本信息
- **项目名称**: LangGraph Agent v3.2
- **技术栈**: Python 3.11+, LangGraph, LangChain, Chainlit, SQLite
- **架构模式**: 基于状态机的智能代理系统
- **开发标准**: 严格遵循 LangGraph 官方标准和 Chainlit 最佳实践

### 核心特性
- 🤖 基于 LangGraph 的状态机工作流
- 💾 完整的 SQLite 持久化存储
- 🛠️ 30+ MCP 工具集成
- 🔐 Chainlit 身份验证系统
- 📊 实时流式输出和交互
- 🏷️ 智能会话命名功能

## 🏗️ 技术架构

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Chainlit UI   │    │   CLI Interface │    │   Web API       │
│   (前端界面)     │    │   (命令行)       │    │   (未来扩展)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      LangGraph Agent      │
                    │     (核心智能体系统)       │
                    └─────────────┬─────────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
┌─────────▼───────┐    ┌─────────▼───────┐    ┌─────────▼───────┐
│   MCP Tools     │    │  LLM Providers  │    │  Persistence    │
│   (工具集成)     │    │  (模型提供商)    │    │  (持久化存储)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心组件

#### 1. 主程序入口
- **`main.py`**: CLI 模式主程序
- **`chainlit_app.py`**: Web 界面主程序

#### 2. 核心模块
- **`llm_loader.py`**: LLM 模型加载器
- **`mcp_loader.py`**: MCP 工具加载器
- **`sqlite_data_layer.py`**: 自定义 SQLite 数据层

#### 3. 配置系统
- **`config/llm_config.json`**: LLM 提供商配置
- **`config/mcp_config.json`**: MCP 工具配置
- **`config/persistence_config.json`**: 持久化配置

## 🛠️ 开发环境配置

### 环境要求
- **Python**: 3.11 或更高版本
- **包管理器**: uv (推荐) 或 pip
- **操作系统**: macOS, Linux, Windows

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd LangGraphAgentv3.2
```

2. **创建虚拟环境**
```bash
# 使用 uv (推荐)
uv venv
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows

# 或使用 Python venv
python -m venv .venv
source .venv/bin/activate
```

3. **安装依赖**
```bash
# 使用 uv
uv sync

# 或使用 pip
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，添加必要的 API 密钥
```

### 开发工具推荐
- **IDE**: VS Code, PyCharm
- **调试**: Python Debugger, Chainlit 内置调试
- **测试**: pytest, pytest-asyncio
- **代码格式化**: black, isort
- **类型检查**: mypy

## 📁 项目结构详解

### 目录结构
```
LangGraphAgentv3.2/
├── 📄 核心文件
│   ├── main.py                    # CLI 主程序
│   ├── chainlit_app.py            # Web 界面主程序
│   ├── llm_loader.py              # LLM 加载器
│   ├── mcp_loader.py              # MCP 工具加载器
│   └── sqlite_data_layer.py       # SQLite 数据层
│
├── 🗂️ 配置目录
│   ├── config/                    # 应用配置
│   │   ├── llm_config.json        # LLM 配置
│   │   ├── mcp_config.json        # MCP 工具配置
│   │   └── persistence_config.json# 持久化配置
│   └── .chainlit/                 # Chainlit 配置
│       ├── config.toml            # UI 配置
│       └── translations/          # 界面翻译
│
├── 💾 数据目录
│   └── data/                      # 数据存储
│       ├── agent_memory.db        # LangGraph 检查点
│       ├── chainlit_history.db    # Chainlit 会话历史
│       └── agent_data.db          # 业务数据
│
├── 📚 文档目录
│   └── docs/                      # 项目文档
│
├── 🧪 测试目录
│   └── tests/                     # 测试文件
│
└── 🛠️ 脚本目录
    └── scripts/                   # 实用脚本
```

### 核心文件详解

#### main.py - CLI 主程序
```python
# 主要功能：
# 1. 初始化 LangGraph Agent
# 2. 配置持久化存储
# 3. 处理命令行交互
# 4. 会话管理

# 关键类和函数：
- initialize_agent()      # 初始化智能体
- SimpleSessionManager    # 会话管理器
- create_workflow()       # 创建工作流
```

#### chainlit_app.py - Web 界面主程序
```python
# 主要功能：
# 1. Chainlit Web 界面集成
# 2. 用户身份验证
# 3. 历史会话管理
# 4. 实时消息处理

# 关键装饰器：
- @cl.password_auth_callback  # 身份验证
- @cl.on_chat_start          # 会话开始
- @cl.on_chat_resume         # 会话恢复
- @cl.on_message             # 消息处理
```

#### llm_loader.py - LLM 加载器
```python
# 支持的提供商：
- 智谱 AI (GLM-4-Flash)
- ModelScope (Qwen)
- Ollama (本地模型)
- OpenAI (兼容接口)

# 主要函数：
- load_llm_from_config()     # 从配置加载 LLM
- create_llm_instance()      # 创建 LLM 实例
```

#### mcp_loader.py - MCP 工具加载器
```python
# 支持的工具类型：
- 搜索工具 (Tavily)
- 图表工具 (Chart)
- 推理工具 (Sequential Thinking)
- 浏览器工具 (Browser)

# 主要函数：
- load_mcp_tools_from_config()  # 加载 MCP 工具
- create_mcp_client()           # 创建 MCP 客户端
```

## 🔧 核心功能实现

### 1. 持久化系统

#### LangGraph 检查点机制
```python
# 使用官方 AsyncSqliteSaver
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver

# 创建检查点存储器
checkpointer = AsyncSqliteSaver.from_conn_string(
    f"sqlite:///{db_path}"
)

# 编译工作流时集成
app = workflow.compile(checkpointer=checkpointer)
```

#### 会话配置标准
```python
# 标准的会话配置格式
config = {
    "configurable": {
        "thread_id": "user_12345678"
    }
}

# 运行时传入配置
async for output in app.astream(inputs, config=config):
    # 处理输出
```

### 2. 状态管理

#### MessagesState 定义
```python
from langgraph.graph import MessagesState

# 使用官方状态模式
class AgentState(MessagesState):
    # 继承官方消息状态
    pass
```

#### 工作流定义
```python
# 创建状态图
workflow = StateGraph(MessagesState)

# 添加节点
workflow.add_node("agent", call_model)
workflow.add_node("tools", ToolNode(tools))

# 添加边
workflow.add_edge(START, "agent")
workflow.add_conditional_edges("agent", should_continue)
workflow.add_edge("tools", "agent")
```

### 3. 工具集成

#### MCP 工具标准
```python
# 工具配置格式
{
    "mcpServers": {
        "tool-name": {
            "command": "npx",
            "args": ["-y", "package-name"],
            "transport": "stdio",
            "env": {
                "API_KEY": "your-key"
            }
        }
    }
}
```

#### 工具调用流程
```python
# 1. 加载工具
tools = load_mcp_tools_from_config()

# 2. 绑定到模型
llm_with_tools = llm.bind_tools(tools)

# 3. 创建工具节点
tool_node = ToolNode(tools)
```

## 🧪 测试系统

### 测试结构
```
tests/
├── test_system_integration.py     # 系统集成测试
├── test_persistence_complete.py   # 持久化功能测试
├── test_complete_functionality.py # 完整功能测试
├── test_thread_naming.py          # 智能命名测试
└── test_chainlit_fixes.py         # Chainlit 修复测试
```

### 运行测试
```bash
# 运行所有测试
uv run pytest tests/

# 运行特定测试
uv run python tests/test_system_integration.py

# 运行持久化测试
uv run python tests/test_persistence_complete.py
```

### 测试覆盖
- ✅ 配置加载测试
- ✅ LLM 初始化测试
- ✅ MCP 工具加载测试
- ✅ 持久化功能测试
- ✅ 会话管理测试
- ✅ 智能命名测试

## 🚀 部署指南

### 开发环境启动
```bash
# CLI 模式
uv run python main.py

# Web 界面模式
uv run chainlit run chainlit_app.py --port 8000
```

### 生产环境部署
```bash
# 使用 Docker (推荐)
docker build -t langgraph-agent .
docker run -p 8000:8000 langgraph-agent

# 或使用 systemd 服务
sudo systemctl enable langgraph-agent
sudo systemctl start langgraph-agent
```

### 环境变量配置
```bash
# 必需的环境变量
CHAINLIT_AUTH_SECRET=your-secret-key
TAVILY_API_KEY=your-tavily-key
ZHIPU_API_KEY=your-zhipu-key
MODELSCOPE_API_KEY=your-modelscope-key
```

## 🔍 故障排除

### 常见问题

#### 1. 数据库表结构错误
```bash
# 症状：table steps has no column named defaultOpen
# 解决：运行修复脚本
uv run python tests/test_chainlit_fixes.py
```

#### 2. MCP 工具加载失败
```bash
# 症状：No matching version found for package
# 解决：检查 mcp_config.json 中的版本号
npm view package-name versions --json
```

#### 3. 端口占用问题
```bash
# 查找占用进程
lsof -i :8000
# 结束进程
kill -9 <PID>
```

### 调试技巧

#### 1. 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 2. 检查数据库状态
```bash
uv run python tests/check_database_status.py
```

#### 3. 验证配置文件
```bash
uv run python scripts/verify_project_structure.py
```

## 📈 性能优化

### 数据库优化
- 定期清理无用会话数据
- 使用索引优化查询性能
- 配置合适的连接池大小

### 内存优化
- 限制会话历史长度
- 及时释放 MCP 客户端连接
- 使用流式处理减少内存占用

### 并发优化
- 使用异步 I/O 处理请求
- 配置合适的工作进程数
- 实现请求队列和限流

## 🔮 扩展指南

### 添加新的 LLM 提供商
1. 在 `llm_loader.py` 中添加新的提供商支持
2. 更新 `config/llm_config.json` 配置
3. 添加相应的测试用例

### 集成新的 MCP 工具
1. 在 `config/mcp_config.json` 中添加工具配置
2. 确保工具包已安装
3. 测试工具功能

### 扩展 Web 界面
1. 修改 `chainlit_app.py` 添加新功能
2. 更新 `.chainlit/config.toml` 配置
3. 添加必要的翻译文件

### 实现多智能体系统
1. 参考 `docs/多智能体实施方案/` 文档
2. 实现监督者模式架构
3. 添加专业化智能体节点

## 📚 参考资源

### 官方文档
- [LangGraph 官方文档](https://langgraph.readthedocs.io/)
- [Chainlit 官方文档](https://docs.chainlit.cn/)
- [LangChain 官方文档](https://python.langchain.com/)

### 项目文档
- [实施路线图](docs/Implementation_Roadmap.md)
- [会话管理指南](docs/SESSION_MANAGEMENT_GUIDE.md)
- [数据库分析报告](docs/数据库文件分析报告.md)

### 社区资源
- [WoodenFish 项目](https://github.com/WoodenFish/WoodenFish)
- [MCP 工具集合](https://github.com/modelcontextprotocol)

---

## 📞 技术支持

如有技术问题，请参考：
1. 项目 README.md 文档
2. docs/ 目录下的详细文档
3. tests/ 目录下的测试用例
4. 项目 Issues 和讨论区

**祝您开发愉快！** 🚀
