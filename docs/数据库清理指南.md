# 数据库清理指南

## 📋 概述

本指南提供了 LangGraph Agent 项目中数据库清理的完整说明，包括自动化脚本使用和手动清理方法。

## 🗂️ 数据库文件说明

项目中包含以下主要数据库文件：

### 1. `chainlit_history.db` - Chainlit 历史数据
- **用途**: 存储用户界面的对话历史
- **主要表**:
  - `threads`: 对话线程信息
  - `steps`: 对话步骤（消息）
  - `elements`: 界面元素
  - `users`: 用户信息
  - `feedbacks`: 用户反馈
- **大小**: 通常 1-5 MB（取决于历史对话数量）

### 2. `agent_memory.db` - LangGraph 检查点数据
- **用途**: 存储 LangGraph Agent 的状态检查点
- **主要表**:
  - `checkpoints`: Agent 状态检查点
  - `writes`: 状态写入记录
- **大小**: 通常 5-20 MB（取决于对话复杂度）

### 3. `agent_data.db` - Agent 业务数据
- **用途**: 存储 Agent 的业务相关数据
- **主要表**:
  - `conversations`: 对话记录
  - `memories`: 记忆数据
  - `sessions`: 会话信息
- **大小**: 通常较小，< 1 MB

## 🛠️ 自动化清理

### 使用清理脚本

项目提供了两个清理脚本：

#### 1. 完整历史数据清理
```bash
# 清理所有历史数据（包含备份）
python scripts/clear_history_data.py
```

**功能**:
- ✅ 自动备份所有数据库
- ✅ 清理 Chainlit 历史对话
- ✅ 清理 LangGraph 检查点
- ✅ 清理 Agent 业务数据
- ✅ 压缩数据库文件
- ✅ 保留表结构

#### 2. 空数据库分析清理
```bash
# 分析数据库状态（预览模式）
python scripts/cleanup_databases.py

# 实际清理空数据库
python scripts/cleanup_databases.py --execute
```

**功能**:
- 🔍 分析数据库使用情况
- 🗑️ 清理空数据库文件
- 📊 提供优化建议

### 清理结果示例

```
🗑️  历史数据清理工具
============================================================
📦 创建数据库备份...
   ✅ 备份: chainlit_history.db -> chainlit_history.db.backup_20250628_133624

🧹 清理 Chainlit 历史数据...
   清理前: 6 个线程, 492 个步骤, 0 个元素
   清理后: 0 个线程, 0 个步骤, 0 个元素
   ✅ 成功清理 6 个线程和 492 个步骤

🗜️  压缩数据库文件...
   ✅ chainlit_history.db: 1.71 MB -> 0.05 MB

🎉 清理完成！
```

## 🔧 手动清理方法

### 1. 使用 SQLite 命令行

```bash
# 连接到数据库
sqlite3 data/chainlit_history.db

# 查看表结构
.tables

# 清理特定表
DELETE FROM steps;
DELETE FROM threads;
DELETE FROM elements;

# 重置自增序列
DELETE FROM sqlite_sequence WHERE name IN ('threads', 'steps', 'elements');

# 压缩数据库
VACUUM;

# 退出
.quit
```

### 2. 使用 Python 脚本

```python
import sqlite3

# 连接数据库
conn = sqlite3.connect('data/chainlit_history.db')
cursor = conn.cursor()

# 清理数据
cursor.execute("DELETE FROM steps")
cursor.execute("DELETE FROM threads")
cursor.execute("DELETE FROM elements")

# 提交更改
conn.commit()
conn.close()
```

## 📦 备份和恢复

### 自动备份

清理脚本会自动创建备份：
- 备份位置: `data/backups/`
- 命名格式: `数据库名.backup_YYYYMMDD_HHMMSS`
- 示例: `chainlit_history.db.backup_20250628_133624`

### 手动备份

```bash
# 创建备份目录
mkdir -p data/backups

# 备份单个数据库
cp data/chainlit_history.db data/backups/chainlit_history.db.backup_$(date +%Y%m%d_%H%M%S)

# 备份所有数据库
for db in data/*.db; do
    cp "$db" "data/backups/$(basename "$db").backup_$(date +%Y%m%d_%H%M%S)"
done
```

### 恢复数据

```bash
# 恢复特定备份
cp data/backups/chainlit_history.db.backup_20250628_133624 data/chainlit_history.db

# 或者重命名恢复
mv data/backups/chainlit_history.db.backup_20250628_133624 data/chainlit_history.db
```

## 🔄 定期维护建议

### 1. 定期清理计划

```bash
# 创建定期清理脚本
cat > scripts/weekly_cleanup.sh << 'EOF'
#!/bin/bash
echo "开始每周数据库清理..."
cd /path/to/your/project
python scripts/clear_history_data.py
echo "清理完成！"
EOF

# 设置可执行权限
chmod +x scripts/weekly_cleanup.sh

# 添加到 crontab（每周日凌晨2点执行）
# 0 2 * * 0 /path/to/your/project/scripts/weekly_cleanup.sh
```

### 2. 监控数据库大小

```bash
# 检查数据库大小
du -h data/*.db

# 详细分析
python scripts/check_database_status.py
```

### 3. 性能优化

```sql
-- 定期执行 VACUUM 压缩数据库
VACUUM;

-- 分析查询计划
EXPLAIN QUERY PLAN SELECT * FROM threads;

-- 重建索引
REINDEX;
```

## ⚠️ 注意事项

### 清理前检查

1. **确认应用已停止**: 避免数据库锁定
2. **创建备份**: 防止数据丢失
3. **检查磁盘空间**: 确保有足够空间进行操作

### 清理后验证

1. **重启应用**: 确保应用正常启动
2. **测试功能**: 验证基本功能正常
3. **检查日志**: 确认没有错误信息

### 安全建议

- 🔒 **定期备份**: 建议每天自动备份重要数据
- 🔍 **监控大小**: 设置数据库大小告警
- 📝 **记录操作**: 保留清理操作日志
- 🧪 **测试恢复**: 定期测试备份恢复流程

## 🆘 故障排除

### 常见问题

1. **数据库锁定**
   ```
   错误: database is locked
   解决: 停止应用后再执行清理
   ```

2. **权限不足**
   ```
   错误: Permission denied
   解决: 检查文件权限，使用 sudo 或修改权限
   ```

3. **磁盘空间不足**
   ```
   错误: No space left on device
   解决: 清理磁盘空间或移动到其他位置
   ```

### 紧急恢复

如果清理后出现问题：

1. **立即停止应用**
2. **恢复最新备份**
3. **检查数据完整性**
4. **重启应用并测试**

## 📞 联系支持

如果遇到问题，请：
1. 检查日志文件
2. 保留错误信息
3. 记录操作步骤
4. 联系技术支持
