# LangGraph Agent 普通用户使用指导书

## 🎯 项目简介

LangGraph Agent 是一个智能对话助手系统，它可以：
- 💬 与您进行自然对话
- 🔍 搜索网络信息回答问题
- 📊 生成图表和可视化内容
- 🧠 进行复杂的逻辑推理
- 💾 记住对话历史，支持多轮对话

## 🚀 快速开始

### 第一步：获取项目文件
确保您已经获得了完整的项目文件夹 `LangGraphAgentv3.2`

### 第二步：安装 Python
1. 访问 [Python 官网](https://www.python.org/downloads/)
2. 下载并安装 Python 3.11 或更高版本
3. 安装时勾选 "Add Python to PATH"

### 第三步：安装 uv 包管理器
打开命令行（终端），运行：
```bash
# Windows (PowerShell)
irm https://astral.sh/uv/install.ps1 | iex

# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 第四步：启动项目
1. 打开命令行，进入项目目录：
```bash
cd LangGraphAgentv3.2
```

2. 安装依赖：
```bash
uv sync
```

3. 启动 Web 界面：
```bash
uv run chainlit run chainlit_app.py
```

4. 打开浏览器访问：http://localhost:8000

### 第五步：登录使用
- 用户名：`admin`
- 密码：`admin123`

## 🖥️ 使用界面介绍

### Web 界面功能
登录后，您将看到一个现代化的聊天界面：

#### 主要区域
- **左侧面板**：历史会话列表
- **中央区域**：当前对话窗口
- **底部输入框**：输入您的问题

#### 功能按钮
- **新建对话**：开始一个全新的对话
- **删除会话**：删除不需要的历史对话
- **设置**：调整界面设置

### 命令行界面（可选）
如果您喜欢命令行，也可以使用：
```bash
uv run python main.py
```

## 💡 基本使用方法

### 1. 开始对话
在输入框中输入您的问题，例如：
- "你好，请介绍一下自己"
- "帮我搜索最新的人工智能发展趋势"
- "请为我生成一个销售数据的柱状图"

### 2. 查看历史对话
- 点击左侧面板中的任意历史会话
- 系统会自动恢复该对话的上下文
- 您可以继续之前的话题

### 3. 管理会话
- **新建会话**：点击"新建对话"按钮
- **删除会话**：在历史会话上右键选择删除
- **重命名会话**：系统会根据首条消息自动命名

### 4. 使用高级功能
- **搜索功能**：询问任何需要网络搜索的问题
- **图表生成**：要求生成各种类型的图表
- **复杂推理**：提出需要多步思考的复杂问题

## 🛠️ 功能详解

### 搜索功能
AI 助手可以搜索最新的网络信息：
```
示例问题：
- "2024年最新的科技趋势是什么？"
- "今天的天气如何？"
- "最近有什么重要新闻？"
```

### 图表生成
支持多种图表类型：
```
示例请求：
- "请生成一个显示月度销售数据的柱状图"
- "制作一个饼图显示市场份额分布"
- "创建一个折线图展示股价变化趋势"
```

### 智能推理
处理复杂的逻辑问题：
```
示例问题：
- "如何制定一个有效的营销策略？"
- "分析这个商业计划的优缺点"
- "帮我规划一次旅行路线"
```

### 文档处理
分析和处理文档内容：
```
示例任务：
- "总结这篇文章的要点"
- "翻译这段文字"
- "改写这个段落使其更专业"
```

## 🔧 常见问题解决

### 安装问题

#### Q: Python 安装失败
**A**: 
1. 确保从官网下载正确版本
2. 以管理员权限运行安装程序
3. 重启电脑后重试

#### Q: uv 安装失败
**A**: 
1. 检查网络连接
2. 尝试使用代理或 VPN
3. 手动下载安装包

#### Q: 依赖安装失败
**A**: 
```bash
# 清理缓存后重试
uv cache clean
uv sync
```

### 运行问题

#### Q: 端口被占用
**A**: 
```bash
# 使用不同端口
uv run chainlit run chainlit_app.py --port 8001
```

#### Q: 无法访问网页
**A**: 
1. 检查防火墙设置
2. 确认端口 8000 未被阻止
3. 尝试使用 127.0.0.1:8000

#### Q: 登录失败
**A**: 
- 确认用户名：`admin`
- 确认密码：`admin123`
- 检查大小写是否正确

### 使用问题

#### Q: AI 回复很慢
**A**: 
1. 检查网络连接
2. 等待模型加载完成
3. 尝试重启程序

#### Q: 搜索功能不工作
**A**: 
1. 检查网络连接
2. 确认 API 密钥配置正确
3. 稍后重试

#### Q: 历史对话丢失
**A**: 
1. 检查 data/ 目录是否存在
2. 确认数据库文件未被删除
3. 重启程序尝试恢复

## 📱 使用技巧

### 提问技巧
1. **具体明确**：详细描述您的需求
2. **分步提问**：复杂问题可以分解为多个小问题
3. **提供上下文**：给出相关背景信息

### 对话管理
1. **及时新建会话**：不同主题使用不同会话
2. **定期清理**：删除不需要的历史会话
3. **善用历史**：利用历史会话继续之前的讨论

### 功能组合
1. **搜索+分析**：先搜索信息，再要求分析
2. **数据+图表**：提供数据后要求可视化
3. **问题+推理**：提出问题后要求深入思考

## 🔒 安全注意事项

### 数据安全
- 所有对话数据存储在本地
- 不会上传个人信息到外部服务器
- 定期备份重要对话记录

### 使用建议
- 不要输入敏感个人信息
- 定期更新系统和依赖
- 注意保护 API 密钥安全

## 🆘 获取帮助

### 自助解决
1. 查看本指导书的常见问题部分
2. 检查项目 README.md 文件
3. 查看 docs/ 目录下的其他文档

### 寻求支持
1. 查看项目的 Issues 页面
2. 在讨论区提问
3. 联系项目维护者

### 有用的命令
```bash
# 检查项目状态
uv run python scripts/verify_project_structure.py

# 清理数据库
uv run python scripts/cleanup_databases.py

# 运行测试
uv run python tests/test_system_integration.py
```

## 🎉 开始使用

现在您已经了解了 LangGraph Agent 的基本使用方法，可以开始探索这个强大的 AI 助手了！

### 建议的第一次使用流程：
1. 启动 Web 界面
2. 使用 admin 账号登录
3. 输入简单问题测试功能
4. 尝试搜索功能
5. 体验图表生成
6. 探索历史会话管理

### 进阶使用：
- 尝试复杂的推理问题
- 使用多轮对话功能
- 组合使用不同功能
- 探索更多 MCP 工具

**祝您使用愉快！** 🚀

---

## 📞 联系信息

如果您在使用过程中遇到任何问题，请：
1. 首先查阅本指导书
2. 查看项目文档
3. 联系技术支持

**感谢您选择 LangGraph Agent！**
