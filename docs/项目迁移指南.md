# 项目迁移指南

## 概述

本指南详细说明了如何将LangGraph Agent项目从一台电脑迁移到另一台电脑，以及如何解决迁移过程中可能遇到的问题。

## 常见迁移问题

### 1. 数据库约束冲突
**错误信息**：
```
UNIQUE constraint failed: threads.id
❌ 创建线程失败: UNIQUE constraint failed: threads.id
❌ 初始化失败: UNIQUE constraint failed: threads.id
```

**问题原因**：
- 多个用户同时访问时，Chainlit可能生成相同的session ID
- 数据库中存在重复的线程ID记录

**解决方案**：
1. **自动重试机制**（已实现）：
   - 在 `sqlite_data_layer.py` 中的 `create_thread` 方法添加了重试逻辑
   - 当遇到UNIQUE约束冲突时，自动生成新的线程ID并重试
   - 最大重试次数为3次

2. **手动清理**：
   ```bash
   # 检查数据库冲突
   python tests/check_database_conflicts.py
   
   # 修复数据库冲突
   python tests/fix_database_conflicts.py
   ```

### 2. npm包版本问题
**错误信息**：
```
npm error notarget No matching version found for tavily-mcp@0.2.4.
npm error notarget In most cases you or one of your dependencies are requesting
npm error notarget a package version that doesn't exist.
```

**问题原因**：
- 配置文件中指定的npm包版本不存在
- 网络环境或npm源不同导致包版本不可用

**解决方案**：
1. **检查可用版本**：
   ```bash
   npm view tavily-mcp versions --json
   ```

2. **更新配置文件**：
   修改 `config/mcp_config.json`：
   ```json
   {
     "mcpServers": {
       "tavily": {
         "command": "npx",
         "args": [
           "-y",
           "tavily-mcp@0.2.3"  // 从0.2.4改为0.2.3
         ],
         "env": {
           "TAVILY_API_KEY": "your-api-key"
         },
         "transport": "stdio"
       }
     }
   }
   ```

### 3. TaskGroup异常
**错误信息**：
```
❌ 初始化失败: unhandled errors in a TaskGroup (1 sub-exception)
```

**问题原因**：
- MCP工具初始化失败
- 网络连接问题
- 依赖包版本冲突

**解决方案**：
1. **检查网络连接**
2. **重新安装依赖**：
   ```bash
   uv sync --reinstall
   ```
3. **检查MCP配置**：
   ```bash
   # 验证MCP配置文件
   python -c "import json; print(json.load(open('config/mcp_config.json')))"
   ```

## 完整迁移步骤

### 1. 环境准备

#### 系统要求
- Python 3.11+
- Node.js 16+
- Git

#### 安装uv包管理器
```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 2. 项目拷贝

#### 方法1：Git克隆（推荐）
```bash
git clone <repository-url>
cd LangGraphAgentv3.2
```

#### 方法2：文件拷贝
```bash
# 确保拷贝所有文件，包括隐藏文件
cp -r /source/path/LangGraphAgentv3.2 /destination/path/
```

### 3. 依赖安装

```bash
# 进入项目目录
cd LangGraphAgentv3.2

# 安装Python依赖
uv sync

# 验证安装
uv run python --version
```

### 4. 配置检查

#### 检查配置文件
```bash
# 检查必要的配置文件是否存在
ls -la config/
# 应该包含：
# - llm_config.json
# - mcp_config.json
# - persistence_config.json
```

#### 检查数据目录
```bash
# 创建数据目录（如果不存在）
mkdir -p data

# 检查权限
ls -la data/
```

### 5. 问题修复

#### 运行诊断脚本
```bash
# 检查数据库状态
python tests/check_database_conflicts.py

# 修复数据库问题
python tests/fix_database_conflicts.py

# 验证项目结构
python scripts/verify_project_structure.py
```

### 6. 启动验证

#### 测试CLI模式
```bash
uv run python main.py
```

#### 测试Web模式
```bash
# 启动Web界面
uv run chainlit run chainlit_app.py --port 8001

# 在浏览器中访问
open http://localhost:8001
```

## 故障排除

### 端口占用问题
```bash
# 查找占用端口的进程
lsof -ti:8000

# 结束进程
lsof -ti:8000 | xargs kill -9

# 使用其他端口
uv run chainlit run chainlit_app.py --port 8001
```

### 权限问题
```bash
# 修复文件权限
chmod +x scripts/*.py
chmod +x tests/*.py

# 修复数据目录权限
chmod 755 data/
```

### 依赖冲突
```bash
# 清理并重新安装
uv clean
uv sync --reinstall

# 检查依赖树
uv tree
```

## 验证清单

迁移完成后，请验证以下功能：

- [ ] 项目可以正常启动（CLI和Web模式）
- [ ] MCP工具正常加载（应显示28个工具）
- [ ] 数据库连接正常
- [ ] 新会话可以创建
- [ ] 历史会话可以查看和删除
- [ ] 对话功能正常工作
- [ ] 持久化功能正常

## 联系支持

如果遇到本指南未涵盖的问题，请：

1. 查看项目日志文件
2. 检查 `docs/` 目录下的其他文档
3. 运行诊断脚本获取详细信息
4. 提交Issue并附上错误日志

## 更新记录

- **2025-06-28**：创建初始版本，包含数据库约束冲突和npm包版本问题的解决方案
