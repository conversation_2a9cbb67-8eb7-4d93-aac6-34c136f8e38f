============================= test session starts ==============================
platform darwin -- Python 3.12.9, pytest-8.4.1, pluggy-1.6.0
rootdir: /Users/<USER>/Desktop/LangGraphAgentv3.2
configfile: pyproject.toml
plugins: anyio-4.9.0, langsmith-0.4.1, asyncio-1.0.0
asyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function
collected 9 items

tests/test_interactive.py ....                                           [ 44%]
tests/test_system_integration.py FFFFF                                   [100%]

=================================== FAILURES ===================================
___________________________ test_basic_functionality ___________________________
async def functions are not natively supported.
You need to install a suitable plugin for your async framework, for example:
  - anyio
  - pytest-asyncio
  - pytest-tornasync
  - pytest-trio
  - pytest-twisted
_______________________________ test_persistence _______________________________
async def functions are not natively supported.
You need to install a suitable plugin for your async framework, for example:
  - anyio
  - pytest-asyncio
  - pytest-tornasync
  - pytest-trio
  - pytest-twisted
___________________________ test_session_management ____________________________
async def functions are not natively supported.
You need to install a suitable plugin for your async framework, for example:
  - anyio
  - pytest-asyncio
  - pytest-tornasync
  - pytest-trio
  - pytest-twisted
______________________________ test_configuration ______________________________
async def functions are not natively supported.
You need to install a suitable plugin for your async framework, for example:
  - anyio
  - pytest-asyncio
  - pytest-tornasync
  - pytest-trio
  - pytest-twisted
______________________________ test_checkpointer _______________________________
async def functions are not natively supported.
You need to install a suitable plugin for your async framework, for example:
  - anyio
  - pytest-asyncio
  - pytest-tornasync
  - pytest-trio
  - pytest-twisted
=========================== short test summary info ============================
FAILED tests/test_system_integration.py::test_basic_functionality - Failed: a...
FAILED tests/test_system_integration.py::test_persistence - Failed: async def...
FAILED tests/test_system_integration.py::test_session_management - Failed: as...
FAILED tests/test_system_integration.py::test_configuration - Failed: async d...
FAILED tests/test_system_integration.py::test_checkpointer - Failed: async de...
=================== 5 failed, 4 passed, 1 warning in 42.95s ====================
