"""
错误处理模块
提供分层异常处理、重试机制和用户友好的错误消息
"""

import asyncio
import logging
from functools import wraps
from typing import Optional, Any, Dict, Callable
from langchain_core.exceptions import LangChainException
from langchain_core.messages import AIMessage

logger = logging.getLogger(__name__)


# 自定义异常类
class AgentError(Exception):
    """Agent 自定义异常基类"""
    def __init__(self, message: str, error_code: str = "AGENT_ERROR", details: Optional[Dict] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}


class ModelCallError(AgentError):
    """模型调用异常"""
    def __init__(self, message: str, model_name: str = "", details: Optional[Dict] = None):
        super().__init__(message, "MODEL_CALL_ERROR", details)
        self.model_name = model_name


class ToolExecutionError(AgentError):
    """工具执行异常"""
    def __init__(self, message: str, tool_name: str = "", details: Optional[Dict] = None):
        super().__init__(message, "TOOL_EXECUTION_ERROR", details)
        self.tool_name = tool_name


class NetworkError(AgentError):
    """网络连接异常"""
    def __init__(self, message: str, url: str = "", details: Optional[Dict] = None):
        super().__init__(message, "NETWORK_ERROR", details)
        self.url = url


class ConfigurationError(AgentError):
    """配置错误异常"""
    def __init__(self, message: str, config_key: str = "", details: Optional[Dict] = None):
        super().__init__(message, "CONFIGURATION_ERROR", details)
        self.config_key = config_key


def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff_factor: float = 2.0):
    """
    重试装饰器 - 支持指数退避
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间(秒)
        backoff_factor: 退避因子
    """
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    if asyncio.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
                        
                except (ConnectionError, TimeoutError, NetworkError) as e:
                    last_exception = e
                    if attempt < max_retries:
                        wait_time = delay * (backoff_factor ** attempt)
                        logger.warning(
                            f"第{attempt + 1}次尝试失败，{wait_time:.1f}秒后重试: {e}",
                            extra={"attempt": attempt + 1, "max_retries": max_retries}
                        )
                        await asyncio.sleep(wait_time)
                    else:
                        logger.error(f"所有重试都失败了: {e}")
                        raise NetworkError(f"网络请求失败，已重试{max_retries}次: {str(e)}")
                        
                except Exception as e:
                    # 对于其他异常，不重试
                    logger.error(f"不可重试的异常: {e}")
                    raise
            
            raise last_exception
            
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 同步版本的重试逻辑
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                    
                except (ConnectionError, TimeoutError, NetworkError) as e:
                    last_exception = e
                    if attempt < max_retries:
                        wait_time = delay * (backoff_factor ** attempt)
                        logger.warning(f"第{attempt + 1}次尝试失败，{wait_time:.1f}秒后重试: {e}")
                        import time
                        time.sleep(wait_time)
                    else:
                        logger.error(f"所有重试都失败了: {e}")
                        raise NetworkError(f"网络请求失败，已重试{max_retries}次: {str(e)}")
                        
                except Exception as e:
                    logger.error(f"不可重试的异常: {e}")
                    raise
            
            raise last_exception
        
        # 根据函数类型返回对应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
            
    return decorator


def handle_agent_errors(func: Callable):
    """
    Agent 错误处理装饰器
    统一处理各种异常并返回用户友好的错误消息
    """
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        try:
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
                
        except ModelCallError as e:
            logger.error(f"模型调用错误: {e.message}", extra={"error_code": e.error_code, "model": e.model_name})
            return {"messages": [AIMessage(content="抱歉，AI模型暂时不可用，请稍后重试。")]}
            
        except ToolExecutionError as e:
            logger.error(f"工具执行错误: {e.message}", extra={"error_code": e.error_code, "tool": e.tool_name})
            return {"messages": [AIMessage(content=f"抱歉，工具 {e.tool_name} 执行失败，请重试或联系管理员。")]}
            
        except NetworkError as e:
            logger.error(f"网络错误: {e.message}", extra={"error_code": e.error_code, "url": e.url})
            return {"messages": [AIMessage(content="抱歉，网络连接出现问题，请检查网络后重试。")]}
            
        except ConfigurationError as e:
            logger.error(f"配置错误: {e.message}", extra={"error_code": e.error_code, "config_key": e.config_key})
            return {"messages": [AIMessage(content="抱歉，系统配置有误，请联系管理员。")]}
            
        except LangChainException as e:
            logger.error(f"LangChain异常: {e}", exc_info=True)
            return {"messages": [AIMessage(content="抱歉，语言模型服务暂时不可用，请稍后重试。")]}
            
        except Exception as e:
            logger.error(f"未知错误: {e}", exc_info=True)
            return {"messages": [AIMessage(content="抱歉，系统出现了未知错误，请联系管理员。")]}
    
    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # 同步版本的错误处理逻辑
            logger.error(f"同步函数错误: {e}", exc_info=True)
            return {"messages": [AIMessage(content="抱歉，处理请求时出现错误，请重试。")]}
    
    # 根据函数类型返回对应的包装器
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper
        
    return decorator


def create_user_friendly_error(error: Exception, context: str = "") -> str:
    """
    创建用户友好的错误消息
    
    Args:
        error: 原始异常
        context: 错误上下文
        
    Returns:
        用户友好的错误消息
    """
    if isinstance(error, ModelCallError):
        return f"AI模型 {error.model_name} 暂时不可用，请稍后重试。"
    elif isinstance(error, ToolExecutionError):
        return f"工具 {error.tool_name} 执行失败，请重试。"
    elif isinstance(error, NetworkError):
        return "网络连接出现问题，请检查网络后重试。"
    elif isinstance(error, ConfigurationError):
        return "系统配置有误，请联系管理员。"
    elif isinstance(error, (ConnectionError, TimeoutError)):
        return "连接超时，请稍后重试。"
    else:
        return f"系统出现错误{f'({context})' if context else ''}，请联系管理员。"
